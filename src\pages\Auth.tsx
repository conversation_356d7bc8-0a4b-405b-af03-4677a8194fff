import React, { useEffect, useState, useCallback, useMemo } from 'react'
import { toast, Toaster } from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'
import LoginForm from '../components/LoginForm'
import SignupForm from '../components/SignupForm'
import ForgotPasswordForm from '../components/ForgotPasswordForm'
import VerifyOtp from '../components/VerifyOtp'
import ResetPassword from '../components/ResetPassword'
import { login, signup, forgotPassword } from '../api/authService'
import bgimg from '../assets/images/login-bg.png'
import bgimgL from '../assets/images/login-bgL.png'
import logo from '../assets/images/logo-white.svg'
import AdService from '../api/addService'
const REACT_APP_GOOGLE_CLIENT_ID = import.meta.env.REACT_APP_GOOGLE_CLIENT_ID;

/**
 * AuthTab lists all possible authentication screens.
 * @typedef {'login' | 'signup' | 'forgotPassword' | 'verifyOtp' | 'resetPassword'} AuthTab
 */
type AuthTab = 'login' | 'signup' | 'forgotPassword' | 'verifyOtp' | 'resetPassword'

/**
 * Props for the Auth component.
 * @interface
 * @property {function(string): void} onLogin - Callback invoked on successful login/signup or OAuth redirect.
 */
interface AuthProps {
  onLogin: (token: string) => void
}

/**
 * Central authentication container handling multiple flows:
 * - Login
 * - Signup
 * - Forgot password (request & verify OTP)
 * - Reset password
 * - OAuth redirect handling
 * @component
 * @param {AuthProps} props
 * @returns {JSX.Element}
 */
const Auth: React.FC<AuthProps> = ({ onLogin }) => {
  const navigate = useNavigate()

  // --- Local component state ---

  /** Currently active authentication tab. */
  const [activeTab, setActiveTab] = useState<AuthTab>('login')
  /** Username/email/phone for OTP verification. */
  const [usernameForOtp, setUsernameForOtp] = useState<string | null>(null)
  /** Token for resetting password after OTP verification. */
  const [resetToken, setResetToken] = useState<string | null>(null)
  /** Loading state for async actions. */
  const [isLoading, setIsLoading] = useState(false)
  /** Referral code from URL (if any). */
  const [referralCode, setReferralCode] = useState<string | undefined>(undefined);

  // --- Handle OAuth redirect with access_token param and referral_code for signup ---
  useEffect(() => {
    // Parse URL params for OAuth token or referral code
    const params = new URLSearchParams(window.location.search)
    const token = params.get('access_token')
    const ref = params.get('ref')
    if (token) {
      onLogin(token)
      navigate('/', { replace: true })
    }
    if (ref) {
      setActiveTab('signup');
      setReferralCode(ref);
    }
  }, [onLogin, navigate])

  // --- Handlers for each auth action ---

  /**
   * Handles user login.
   * @param {string} identifier - Email or username.
   * @param {string} password - User password.
   */
  const handleLogin = useCallback(
    async (identifier: string, password: string) => {
      setIsLoading(true)
      try {
        const { access_token } = await login({ email: identifier, password })
        onLogin(access_token)
        toast.success('Login successful!')
        navigate('/', { replace: true })
      } catch (err) {
        toast.error(err instanceof Error ? err.message : 'Login failed')
      } finally {
        setIsLoading(false)
      }
    },
    [navigate, onLogin]
  )

  /**
   * Handles user signup.
   * @param {string} first_name
   * @param {string} last_name
   * @param {string} phone_number
   * @param {string} email
   * @param {string} password
   * @param {boolean} isAdult
   * @param {string} [referral_code]
   */
  const handleSignup = useCallback(
    async (
      first_name: string,
      last_name: string,
      phone_number: string,
      email: string,
      password: string,
      isAdult: boolean,
      referral_code?: string
    ) => {
      setIsLoading(true)
      try {
        const signupData = {
          first_name,
          last_name,
          email,
          password,
          phone_number,
          isAdult,
          referral_code: referral_code || ''
        }
        const { access_token } = await signup(signupData)
        onLogin(access_token)
        toast.success('Account created successfully!')
        setActiveTab('login')
        AdService.showInterstitialAd();
      } catch (err) {
        toast.error(err instanceof Error ? err.message : 'Signup failed')
      } finally {
        setIsLoading(false)
      }
    },
    [onLogin]
  )

  /**
   * Handles forgot password request.
   * @param {string} username - Email or phone number.
   */
  const handleForgotPassword = useCallback(
    async (username: string) => {
      try {
        await forgotPassword({ username })
        setUsernameForOtp(username)
        toast.success('OTP sent to your registered email/phone')
        setActiveTab('verifyOtp')
      } catch (err) {
        toast.error(err instanceof Error ? err.message : 'Failed to send OTP')
      }
    },
    []
  )

  /**
   * Handles OTP verification for password reset.
   * @param {string} token - OTP token.
   */
  const handleVerifyOtp = useCallback(
    (token: string) => {
      setResetToken(token)
      toast.success('OTP verified!')
      setActiveTab('resetPassword')
    },
    []
  )

  /**
   * Initiates OAuth via Google.
   */
  const handleGoogleAuth = () => {
    console.log("click on google button")
    const params = new URLSearchParams({
      access_type: 'offline',
      client_id: REACT_APP_GOOGLE_CLIENT_ID || '',
      redirect_uri: `${window.location.origin}/user/oauth/google/res`,
      response_type: 'code',
      scope: 'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile',
      state: 'auth'
    })
    window.location.href = `https://accounts.google.com/o/oauth2/auth?${params.toString()}`
  }

  /**
   * Memoized mapping from tab to its corresponding component.
   */
  const tabComponent = useMemo(() => ({
    login: (
      <LoginForm
        isLoading={isLoading}
        onLogin={handleLogin}
        onSwitchTab={() => setActiveTab('signup')}
        onForgotPassword={() => setActiveTab('forgotPassword')}
        onGoogleAuth={handleGoogleAuth}
        error="" // Provide an empty string or appropriate error message
      />
    ),
    signup: (
      <SignupForm
        isLoading={isLoading}
        onSignup={handleSignup}
        onSwitchTab={() => setActiveTab('login')}
        onGoogleAuth={handleGoogleAuth}
        error="" // Provide an appropriate error message or keep it as an empty string
        referral_code={referralCode}
      />
    ),
    forgotPassword: (
      <ForgotPasswordForm
        onForgotPassword={handleForgotPassword}
        onSwitchTab={() => setActiveTab('login')}
        error="" // Pass an empty string or appropriate error message
      />
    ),
    verifyOtp: usernameForOtp ? (
      <VerifyOtp
        username={usernameForOtp}
        onSwitchTab={()=>setActiveTab('forgotPassword')}
        handleVerifyOtp={handleVerifyOtp}
      />
    ) : null,
    resetPassword: resetToken ? (
      <ResetPassword
        token={resetToken}
        onSwitchTab={() => setActiveTab('login')}
      />
    ) : null
  } as Record<AuthTab, JSX.Element | null>), [
    activeTab,
    isLoading,
    usernameForOtp,
    resetToken,
    handleLogin,
    handleSignup,
    handleForgotPassword,
    handleVerifyOtp,
    handleGoogleAuth,
    referralCode
  ])

  // --- Render UI ---
  return (
    <div className="min-h-screen py-8 flex flex-col items-center justify-start relative">
      {/* Toast notifications */}
      <Toaster position="top-right" />

      {/* Background Image + Overlay */}
      <div className="fixed inset-0 -z-10 md:hidden">
        <img
          src={bgimg}
          alt="Background"
          className="w-full h-full object-cover object-center"
        />
      </div>
      <div className="fixed inset-0 -z-10 hidden md:flex">
        <img
          src={bgimgL}
          alt="Background"
          className="w-full h-full object-cover object-center-bottom"
        />
      </div>

      {/* Sunset Gradient Overlay */}
      <div className="fixed inset-0 -z-35">
        <div
          className="
            absolute inset-0
            bg-gradient-to-br
            from-red-400
            via-orange-900
            opacity-10
          "
        />
      </div>

      <div className="w-full max-w-md px-4 relative z-10">
        {/* Header */}
        <header className="text-center relative mb-6">
          <img
            src={logo}
            alt="Logo"
            className="w-[250px] h-auto mx-auto flex mt-3"
          />
          <p className="mt-2 text-white font-[Poppins]">
            Your journey from streets to success begins here
          </p>
        </header>

        <div className="bg-[#131313] rounded-[24px] shadow-2xl backdrop-blur-lg overflow-hidden">
          {/* Toggle Tabs for login/signup only */}
          {(activeTab === 'login' || activeTab === 'signup') && (
            <nav className="flex bg-black/40 p-1 rounded-full mx-4 mt-4">
              {['signup', 'login'].map(tab => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as AuthTab)}
                  className={`
                    flex-1 py-2 px-4
                    rounded-full
                    text-[20px]
                    tracking-wider
                    font-normal uppercase font-[Anton]
                    transition-all duration-200
                    ${activeTab === tab
                      ? 'bg-white text-black'
                      : 'text-white/40'}
                  `}
                >
                  {tab === 'login' ? 'SIGN IN' : 'SIGN UP'}
                </button>
              ))}
            </nav>
          )}

          {/* Set a fixed height for the tab content container */}
          <section className="px-6 pt-6 pb-8">
            {tabComponent[activeTab]}
          </section>
        </div>
      </div>
    </div>
  )
}

export default Auth
