import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  TrendingUp,
  Coins,
  History,
  ChevronLeft,
  Bitcoin,
  ArrowUp,
  ArrowDown,
  Clock,
} from 'lucide-react';
import { useCoins } from '../hooks/useCoinsQuery';
import {
  getCryptoPrice,
  getTradeHistory,
  PlaceCryptoBet,
} from '../api/cryptoKingService';
import { useSocketContext } from '../context/socketProvider';
import { useQuery, useMutation, useInfiniteQuery } from '@tanstack/react-query';
import { useAuth } from '../auth/AuthContext';
import { CustomToast } from '../utils/validations/customeToast';
import SoundManager from '../components/soundManager/SoundManager';
import winSound from '../Sounds/CryptoKing/win_sound.m4a';
import BackGroundSound from '../Sounds/CryptoKing/crypto_king_background.mp3';
import Transactions from '../components/Transactions';
import { useGameDataLoading } from '../hooks/useGameDataLoading';
import { GameLoadingScreen } from '../components/GameLoadingScreen/GameLoadingScreen';
import { BET_ERROR, INT_ERROR, ToastTypes } from '../constant/strings';
import { PzButton } from '../components/Shared/PzButton';
import { COLORS } from '../constant/theming';
/**
 * Props interface for CryptoKing component
 * @typedef {Object} CryptoKingProps
 * @property {Object} balance - User balance object
 * @property {number} balance.coins - Current coin balance
 * @property {Function} onBalanceChange - Callback function to update balance
 */
interface CryptoKingProps {
  // balance: {
  //   coins: number;
  // };
  // onBalanceChange: (newBalance: { coins: number }) => void;
}

/**
 * Game simulation constants
 */
const SIMULATION_DURATION = 10000; // 10 seconds - Total duration for each game simulation
const TICK_RATE = 50; // 50ms per tick - How often the price updates during simulation
const VOLATILITY = 0.005; // Increased volatility for more dramatic price movements

/**
 * Available prediction types for betting
 * Each type has an id, display label, icon component, color theme, and disabled state
 */
const PREDICTION_TYPES = [
  {
    id: 'HIGHER',
    label: 'Higher',
    icon: ArrowUp,
    color: 'bg-green-500',
    disabled: false,
  },
  {
    id: 'LOWER',
    label: 'Lower',
    icon: ArrowDown,
    color: 'bg-red-500',
    disabled: false,
  },
  {
    id: 'RANGE',
    label: 'Range',
    icon: TrendingUp,
    color: `bg-[${COLORS.primary}]`,
    disabled: false,
  },
  {
    id: 'TIME',
    label: 'Time',
    icon: Clock,
    color: 'bg-blue-500',
    disabled: false,
  },
] as const;

/**
 * Type definition for prediction types
 */
type PredictionType = (typeof PREDICTION_TYPES)[number]['id'];

/**
 * Formats a numeric value as USD currency without decimal places
 * @param {string|number} value - The value to format
 * @returns {string} Formatted currency string
 */
const formatPrice = (value: string | number) =>
  Number(value).toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });

/**
 * Formats a number with thousand separators
 * @param {number} value - The number to format
 * @returns {string} Formatted number string
 */
const formatCoins = (value: number) => value?.toLocaleString();

interface CryptoKingProps {
  onWin: (reward: { type: string; amount?: number }) => void;
  onPurchase: (cost: number) => void;
  balance: number;
}

/**
 * CryptoKing - Main crypto trading game component
 * Allows users to place bets on cryptocurrency price movements with real-time simulation
 * @param {CryptoKingProps} props - Component props (currently unused but defined for future use)
 * @returns {JSX.Element} The rendered CryptoKing component
 */
const CryptoKing: React.FC<CryptoKingProps> = ({
  onWin,
  onPurchase,
  balance,
}) => {
  /**
   * Socket context for real-time communication
   */
  const { socketState, connectSocket, setSocketState, isConnected } =
    useSocketContext();

  /**
   * React Router navigation hook
   */
  const navigate = useNavigate();

  /**
   * Custom hook for managing user coins
   */
  const { coins, addCoins } = useCoins();
  const [didWin, setDidWin] = useState(false);
  /**
   * Authentication context for API calls
   */
  const { accessToken } = useAuth();

  /**
   * State for current bet amount
   */
  const [currentBet, setCurrentBet] = useState<number | string>(100);

  /**
   * State for potential winning amount display
   */
  const [potentialWinAmount, setPotentialWinAmount] = useState<number>(0);

  /**
   * State to track if game simulation is currently running
   */
  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  /**
   * Current cryptocurrency price state
   */
  const [currentPrice, setCurrentPrice] = useState<number>(0);

  /**
   * State to control result overlay visibility
   */
  const [showResult, setShowResult] = useState<boolean>(false);

  /**
   * Currently selected prediction type
   */
  const [prediction, setPrediction] = useState<PredictionType>('HIGHER');

  /**
   * Price range settings for RANGE prediction type
   */
  const [priceRange, setPriceRange] = useState<{ min: number; max: number }>({
    min: 0,
    max: 0,
  } as const);

  /**
   * Time target for TIME prediction type (in seconds)
   */
  const [timeTarget, setTimeTarget] = useState<any>(5); // seconds
  const [showTransactionModal, setShowTransactionModal] = useState(false);

  /**
   * Remaining time in current game simulation
   */
  const [timeRemaining, setTimeRemaining] =
    useState<number>(SIMULATION_DURATION);

  /**
   * Ref to store the game loop interval ID for cleanup
   */
  const gameLoopRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Ref to store the starting price for comparison calculations
   */
  const startPriceRef = useRef<number>(0);

  /**
   * Ref for the HTML5 canvas element used for price chart
   */
  const canvasRef = useRef<HTMLCanvasElement>(null);

  /**
   * Ref to store price history data for chart rendering
   */
  const priceHistoryRef = useRef<{ price: number; time: number }[]>([]);

  /**
   * Ref to store the game start timestamp
   */
  const startTimeRef = useRef<number>(0);

  /**
   * Minimum allowed bet amount
   */
  const MIN_BET = 1;

  /**
   * Maximum allowed bet amount (limited by user coins or 1000)
   */
  const MAX_BET = Math.min(coins, 1000);

  /**
   * Effect to scroll to top when component mounts
   */
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  /**
   * Cleanup effect to clear intervals and reset socket state on unmount
   */
  useEffect(() => {
    return () => {
      // Clear any active game loops
      if (gameLoopRef.current) {
        clearInterval(gameLoopRef.current);
      }

      // Set socket state to null when component unmounts
      if (setSocketState) {
        setSocketState(null);
      }
    };
  }, [setSocketState]);

  /**
   * React Query hook to fetch current cryptocurrency price
   * Refetches every 30 seconds when not playing
   */
  const {
    data: cryptoPrice,
    refetch: refetchPrice,
    isLoading: isPriceLoading,
  } = useQuery({
    queryKey: ['cryptoPrice'],
    queryFn: () => {
      if (!accessToken) throw new Error('Access token missing');
      return getCryptoPrice(accessToken);
    },
    refetchOnMount: true,
    enabled: !!accessToken,
  });

  /**
   * React Query infinite scroll hook for trade history
   * Fetches 10 records per page with pagination support
   */
  const {
    data: historyPages,
    refetch,
    hasNextPage,
    isLoading: isHistoryLoading,
  } = useInfiniteQuery({
    queryKey: ['history'],
    queryFn: ({ pageParam = 1 }) => {
      if (!accessToken) throw new Error('Access token missing');

      return getTradeHistory({ page: pageParam, per_page: 10 }, accessToken);
    },

    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      // Determine if more pages exist
      if (
        !lastPage?.data?.total_pages ||
        allPages.length >= lastPage.data?.total_pages
      )
        return undefined;
      return allPages.length + 1;
    },
    enabled: !!accessToken,
  });

  /**
   * Effect to initialize price and chart when crypto price data is received
   */
  useEffect(() => {
    if (cryptoPrice && cryptoPrice.price) {
      setCurrentPrice(cryptoPrice.price);
      startPriceRef.current = cryptoPrice.price;
      // Draw the initial graph
      if (canvasRef.current) {
        drawGraph();
      }
    }
  }, [cryptoPrice]);

  /**
   * Draws the price chart on the HTML5 canvas
   * Includes grid lines, price labels, time labels, and price line
   */
  const drawGraph = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear the entire canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Define padding for chart margins
    const padding = { left: 60, right: 40, top: 40, bottom: 40 };
    const graphWidth = canvas.width - (padding.left + padding.right);
    const graphHeight = canvas.height - (padding.top + padding.bottom);

    // Calculate price range for y-axis scaling
    const prices = priceHistoryRef.current.map((p) => p.price);
    const minPrice = Math.min(...prices, startPriceRef.current * 0.95);
    const maxPrice = Math.max(...prices, startPriceRef.current * 1.05);
    const priceRange = maxPrice - minPrice;
    const yScale = graphHeight / priceRange;

    // Configure grid line styling
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.font = '12px sans-serif';

    // Draw horizontal price grid lines (5 lines)
    for (let i = 0; i <= 5; i++) {
      const price = minPrice + (priceRange * i) / 5;
      const y = canvas.height - padding.bottom - (price - minPrice) * yScale;

      // Draw grid line
      ctx.beginPath();
      ctx.moveTo(padding.left, y);
      ctx.lineTo(canvas.width - padding.right, y);
      ctx.stroke();

      // Draw price label
      ctx.fillText(formatPrice(price), padding.left - 10, y);
    }

    // Draw vertical time grid lines (6 lines for 0-10 seconds)
    for (let i = 0; i <= 5; i++) {
      const x = padding.left + (graphWidth * i) / 5;
      const timeValue = (10 * i) / 5;

      // Draw grid line
      ctx.beginPath();
      ctx.moveTo(x, padding.top);
      ctx.lineTo(x, canvas.height - padding.bottom);
      ctx.stroke();

      // Draw time label
      ctx.textAlign = 'center';
      ctx.fillText(`${timeValue}s`, x, canvas.height - padding.bottom + 20);
    }

    // Draw the main price line if we have data points
    if (priceHistoryRef.current.length > 1) {
      ctx.beginPath();
      ctx.strokeStyle = COLORS.primary; // Yellow/orange color
      ctx.lineWidth = 2;

      // Plot each price point and connect with lines
      priceHistoryRef.current.forEach((point, index) => {
        const x = padding.left + point.time * (graphWidth / 10);
        const y =
          canvas.height - padding.bottom - (point.price - minPrice) * yScale;

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      ctx.stroke();

      // Draw current price indicator dot
      const lastPoint =
        priceHistoryRef.current[priceHistoryRef.current.length - 1];
      const x = padding.left + lastPoint.time * (graphWidth / 10);
      const y =
        canvas.height - padding.bottom - (lastPoint.price - minPrice) * yScale;

      ctx.beginPath();
      ctx.arc(x, y, 4, 0, Math.PI * 2);
      ctx.fillStyle = COLORS.primary;
      ctx.fill();
    }

    // Draw prediction range overlay for RANGE bets
    if (prediction === 'RANGE' && isPlaying) {
      const rangeMinY =
        canvas.height - padding.bottom - (priceRange - minPrice) * yScale;
      const rangeMaxY =
        canvas.height - padding.bottom - (priceRange - minPrice) * yScale;

      // Semi-transparent range indicator
      ctx.fillStyle = 'rgba(245, 158, 11, 0.1)';
      ctx.fillRect(
        padding.left,
        rangeMaxY,
        canvas.width - padding.left - padding.right,
        rangeMinY - rangeMaxY
      );
    }
  };

  /**
   * React Query mutation for placing bets
   * Handles socket connection, bet validation, and game initialization
   */
  const placeBetMutation = useMutation({
    mutationFn: async () => {
      // Ensure socket connection before placing bet
      if (!isConnected) {
        connectSocket();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      // Validate bet amount against available coins
      if (Number(currentBet) > coins) return;
      if (!accessToken) throw new Error('Access token missing');

      // API call to place the bet
      const result = await PlaceCryptoBet(
        {
          bet_amount: Number(currentBet),
          max_value: priceRange?.max,
          min_value: priceRange?.min,
          second: parseInt(timeTarget),
          type: prediction,
        },
        accessToken
      );

      // Store potential win amount from response
      if (result) {
        setPotentialWinAmount(result?.data?.potential_win_amount);
        // setDidWin(true)
      }
      onPurchase(Number(currentBet));
    },
    onSuccess: () => {
      // Scroll to top for better UX
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);

      // Show success notification
      CustomToast('success', 'Bet placed! Good luck!');

      // Initialize game state
      setIsPlaying(true);
      setShowResult(false); // Make sure to hide the result overlay
      startTimeRef.current = Date.now();
      startGame();
    },
    onError: () => CustomToast(ToastTypes.ERROR, BET_ERROR),
  });

  /**
   * Effect to setup canvas dimensions and initial draw
   */
  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      // Set canvas resolution for crisp rendering
      canvas.width = canvas.offsetWidth * window.devicePixelRatio;
      canvas.height = canvas.offsetHeight * window.devicePixelRatio;
      drawGraph();
    }
  }, []);

  /**
   * Effect to initialize price data and chart when crypto price is received
   */
  useEffect(() => {
    if (cryptoPrice && cryptoPrice.price && !isNaN(cryptoPrice.price)) {
      setCurrentPrice(Number(cryptoPrice.price));

      // Set the starting price reference for future calculations
      startPriceRef.current = Number(cryptoPrice.price);

      // Initialize the price history with the current price and time 0
      priceHistoryRef.current = [{ price: Number(cryptoPrice.price), time: 0 }];

      // Draw the initial graph
      if (canvasRef.current) {
        drawGraph();
      }
    }
  }, [cryptoPrice]);

  /**
   * Effect to handle canvas animation during gameplay
   * Uses requestAnimationFrame for smooth 60fps updates
   */
  useEffect(() => {
    let frameId: number | undefined = undefined;

    /**
     * Animation loop function for smooth chart updates
     */
    const animate = () => {
      drawGraph();
      frameId = requestAnimationFrame(animate);
    };

    if (isPlaying) {
      // Cancel any previous animation before starting a new one
      if (frameId) {
        cancelAnimationFrame(frameId);
      }
      frameId = requestAnimationFrame(animate);
    } else {
      // When not playing, just ensure the graph is drawn once
      if (canvasRef.current) {
        drawGraph();
      }
    }

    // Cleanup function
    return () => {
      if (frameId) {
        cancelAnimationFrame(frameId); // Cleanup on unmount or when dependencies change
      }
    };
  }, [isPlaying]);

  /**
   * Starts the game simulation with price movements
   * Sets up interval-based price updates and game timer
   */
  const startGame = () => {
    // Clear any existing game loop
    if (gameLoopRef.current) {
      clearInterval(gameLoopRef.current);
    }

    // Initialize game state
    setIsPlaying(true);
    setShowResult(false);
    setTimeRemaining(SIMULATION_DURATION);
    startTimeRef.current = Date.now();

    // Reset price history with current price
    priceHistoryRef.current = [{ price: currentPrice, time: 0 }];

    // Redraw the graph with clean state
    if (canvasRef.current) {
      drawGraph();
    }

    // Start the main game loop
    gameLoopRef.current = setInterval(() => {
      const now = Date.now();
      const elapsed = now - startTimeRef.current;

      // Check if game duration has ended
      if (elapsed >= SIMULATION_DURATION) {
        handleGameEnd();
        return;
      }

      // Update remaining time display
      setTimeRemaining(SIMULATION_DURATION - elapsed);

      // Update price with volatility simulation
      setCurrentPrice((prev) => {
        const randomFactor = Math.random();
        let priceChange;

        // 10% chance of high volatility movement
        if (randomFactor < 0.1) {
          priceChange = prev * VOLATILITY * 3 * (Math.random() - 0.5);
        } else {
          // Normal volatility movement
          priceChange = prev * VOLATILITY * (Math.random() - 0.5);
        }

        const newPrice = prev + priceChange;
        const elapsedSeconds = elapsed / 1000;

        // Add new price point to history
        priceHistoryRef.current.push({ price: newPrice, time: elapsedSeconds });

        return newPrice;
      });
    }, TICK_RATE);
  };

  /**
   * Handles game end logic and result calculation
   * Determines win/loss based on prediction type and actual price movement
   */
  const handleGameEnd = () => {
    // Clear the game loop
    if (gameLoopRef.current) {
      clearInterval(gameLoopRef.current);
    }

    // Add winning amount to user balance if won
    if (showResult && socketState?.won_amount) {
      addCoins(socketState?.won_amount);
      // setDidWin(true)
    }

    // Update UI state
    setShowResult(true);
    setIsPlaying(false);
    refetchPrice(); // Refetch actual price
    refetch(); // Refresh trade history
    onWin({ type: 'coins' });
  };

  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout> | undefined;

    if (socketState?.won_status === 'WON') {
      setDidWin(true);

      // Automatically reset after 2 seconds
      timeoutId = setTimeout(() => {
        setDidWin(false);
      }, 2000);
    } else {
      setDidWin(false);
    }

    return () => {
      // Cleanup timeout on unmount or win_status change
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [socketState?.won_status]);

  /**
   * Handles bet placement button click
   * Resets chart if showing previous result, then places new bet
   */
  const handlePlaceBet = () => {
    // If we're showing a result, reset the graph first
    if (showResult) {
      setShowResult(false);
      priceHistoryRef.current = [{ price: currentPrice, time: 0 }];
      if (canvasRef.current) {
        drawGraph();
      }
    }

    placeBetMutation.mutate();
  };

  /**
   * Doubles the current bet amount (up to maximum allowed)
   */
  const doubleBet = () => {
    setCurrentBet((prev) => Math.min(Number(prev) * 2, MAX_BET));
  };

  /**
   * Handles time target input changes with validation
   * Ensures value is between 1-10 seconds
   * @param {Event} e - Input change event
   */
  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Allow empty input, but set to 1
    if (value === '') {
      setTimeTarget(1);
      return;
    }

    // Only allow numeric values
    if (!/^\d+$/.test(value)) return;

    const numericValue = parseInt(value, 10);

    // Ensure value is between 1 and 10
    if (numericValue < 1) {
      setTimeTarget(1);
    } else if (numericValue > 10) {
      setTimeTarget(10);
    } else {
      // Value is valid, between 1-10
      setTimeTarget(numericValue);
    }
  };

  /**
   * Renders prediction-specific input fields
   * Shows different inputs based on selected prediction type
   * @returns {JSX.Element|null} Rendered input fields or null
   */
  const renderPredictionInputs = () => {
    switch (prediction) {
      case 'RANGE':
        // Min/Max price input fields for range betting
        return (
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm text-white/60 mb-2">
                Minimum Price
              </label>
              <input
                type="number"
                inputMode="numeric"
                value={priceRange.min}
                onChange={(event) => {
                  const value = event.target.value;
                  // Only allow up to 8 digits
                  if (value.length <= 8) {
                    setPriceRange((prev) => ({
                      ...prev,
                      min: parseInt(value) || 0,
                    }));
                  }
                }}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-yellow-400"
              />
            </div>
            <div>
              <label className="block text-sm text-white/60 mb-2">
                Maximum Price
              </label>
              <input
                type="number"
                inputMode="numeric"
                value={priceRange.max}
                onChange={(e) => {
                  const value = e.target.value;
                  // Only allow up to 8 digits
                  if (value.length <= 8) {
                    setPriceRange((prev) => ({
                      ...prev,
                      max: parseInt(value) || 0,
                    }));
                  }
                }}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-yellow-400"
              />
            </div>
          </div>
        );
      case 'TIME':
        // Time target input for time-based betting
        return (
          <div className="mb-6">
            <label className="block text-sm text-white/60 mb-2">
              Target Time (seconds)
            </label>
            <input
              inputMode="numeric"
              type="number"
              value={timeTarget}
              onChange={handleTimeChange}
              className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-yellow-400"
            />
          </div>
        );
      default:
        // No additional inputs for HIGHER/LOWER predictions
        return null;
    }
  };
  const onClose = () => {
    setShowTransactionModal(false);
  };

  const { isGameDataLoading, visibility } = useGameDataLoading(
    !isPriceLoading,
    !isHistoryLoading,
    isConnected,
    isFinite(coins)
  );

  return (
    <>
      {isGameDataLoading && <GameLoadingScreen />}
      <div
        className="min-h-screen text-text-primary relative"
        style={{
          visibility,
        }}
      >
        <SoundManager
          sounds={{
            background: BackGroundSound,
            win: winSound,
          }}
          // loop the background while the game is running:
          backgroundKey={isPlaying ? 'background' : null}
          // play one of these exactly once when it changes:
          playKey={didWin ? 'win' : undefined}
          volumes={{
            background: 0.4,
            win: 1.0,
          }}
        />
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

        <div className="max-w-4xl mx-auto relative">
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={() => navigate('/')}
              className="text-white/60 hover:text-white transition-colors"
            >
              <ChevronLeft size={24} />
            </button>
            <h1 className="text-2xl font-bold">Crypto King</h1>
          </div>

          <div className="space-y-6">
            <div className="space-y-6">
              <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Bitcoin size={24} color={COLORS.primary} />
                    <span className="text-xl font-bold">
                      {cryptoPrice && formatPrice(cryptoPrice.price || 0)}
                    </span>
                  </div>
                  {isPlaying && (
                    <div className="text-sm text-white/60">
                      Time: {(timeRemaining / 1000).toFixed(1)}s
                    </div>
                  )}
                </div>

                <div className="relative">
                  <canvas ref={canvasRef} height={600} className="w-full" />

                  {showResult && !isPlaying && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm rounded-xl animate-fade-in">
                      <div className="text-center">
                        <h3 className="text-2xl font-bold mb-4">
                          Trading Closed
                        </h3>
                        <div className="space-y-2 mb-6">
                          <p className="text-lg">
                            Closing Price:{' '}
                            <span className="font-bold">
                              {formatPrice(socketState?.current_value)}
                            </span>
                          </p>
                          <p
                            className={`text-lg ${
                              socketState?.won_status === 'WON'
                                ? 'text-green-400'
                                : 'text-red-400'
                            }`}
                          >
                            {socketState?.won_amount > 0 ? '+' : ''}
                          </p>
                        </div>

                        {/* Updated result message to clearly indicate outcome */}
                        <p
                          className={`text-xl font-bold ${
                            socketState?.won_status === 'WON'
                              ? 'text-green-400'
                              : 'text-red-400'
                          }`}
                        >
                          {socketState?.won_status === 'WON'
                            ? `You won ${formatCoins(
                                parseFloat(socketState?.won_amount)
                              )} points!`
                            : 'You lost this trade'}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <PzButton
                text="Place Bet"
                onClick={handlePlaceBet}
                isDisabled={isPlaying || Number(currentBet) <= 0}
              />

              <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="block text-sm text-white/60 mb-2">
                      Bet Amount
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        inputMode="numeric"
                        value={currentBet}
                        disabled={isPlaying}
                        onChange={(event) => {
                          const currentValue = event.target.value;
                          setPriceRange((prev) => ({
                            ...prev,
                            min: parseInt(currentValue) || 0,
                          }));
                          setCurrentBet(
                            currentValue ? Number(currentValue) : ''
                          );
                        }}
                        onBlur={(event) => {
                          const isFloat = !Number.isInteger(
                            Number(event.target.value)
                          );
                          isFloat && CustomToast(ToastTypes.ERROR, INT_ERROR);
                        }}
                        className={`w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 focus:outline-none focus:border-[${COLORS.primary}] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]`}
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-2">
                        <button
                          onClick={() =>
                            setCurrentBet((prev) =>
                              Math.max(MIN_BET, Number(prev) / 2)
                            )
                          }
                          disabled={isPlaying}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors"
                        >
                          ½
                        </button>
                        <button
                          onClick={doubleBet}
                          disabled={isPlaying}
                          className="text-sm bg-white/10 px-2 py-1 rounded hover:bg-white/20 transition-colors"
                        >
                          2×
                        </button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm text-white/60 mb-2">
                      Available Balance
                    </label>
                    <div className="flex items-center gap-2 px-2 py-2 bg-white/5 border border-white/10 rounded-lg">
                      <Coins
                        size={16}
                        color={COLORS.primary}
                        className="text-yellow-400"
                      />
                      <span>{formatCoins(Math.floor(balance))}</span>
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm text-white/60 mb-2">
                    Prediction Type
                  </label>
                  <div className="grid grid-cols-3 gap-4">
                    {PREDICTION_TYPES.map((type) => {
                      const IconComponent = type.icon;
                      return (
                        <button
                          key={type.id}
                          disabled={type?.disabled}
                          onClick={() => setPrediction(type.id)}
                          className={`flex items-center justify-center gap-2 p-3 rounded-lg transition-colors
                        ${
                          type?.disabled
                            ? 'bg-gray-600 text-gray-800 cursor-not-allowed' // disabled style
                            : prediction === type.id
                            ? `${type.color} ${
                                type.id === 'RANGE'
                                  ? 'text-black'
                                  : 'text-white'
                              }`
                            : 'bg-white/5 hover:bg-white/10'
                        }`}
                        >
                          <IconComponent size={20} />
                          {type.label}
                        </button>
                      );
                    })}
                  </div>
                </div>

                {renderPredictionInputs()}

                <div className="flex items-center justify-between mb-4">
                  {/* // In the render section where it shows "Predicted Price" */}
                  <div className="text-sm text-white/60">
                    Predicted Price:{' '}
                    {!currentPrice
                      ? 'Loading...'
                      : prediction === 'RANGE'
                      ? `${formatPrice(priceRange.min)} - ${formatPrice(
                          priceRange.max
                        )}`
                      : prediction === 'HIGHER'
                      ? `Above ${formatPrice(
                          isPlaying ? socketState?.current_value : currentPrice
                        )}`
                      : `Below ${formatPrice(
                          isPlaying ? socketState?.current_value : currentPrice
                        )}`}
                  </div>

                  <div className="text-sm text-white/60">
                    Potential Win: {formatCoins(potentialWinAmount)} points
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-surface-card/90 backdrop-blur-sm rounded-2xl p-6">
              <h2 className="text-lg font-[Anton] mb-4 flex items-center gap-2">
                <History size={20} color={COLORS.primary} />
                Trade History
              </h2>
              <div className="space-y-4">
                {historyPages?.pages[0]?.data?.histories &&
                  historyPages?.pages
                    .flatMap((page) => page.data?.histories || [])
                    .map(
                      (
                        item: {
                          won_status: string;
                          won_amount: string;
                          bet_amount: number;
                          timestamp: string;
                          start_crypto_value: string;
                          end_crypto_value: string;
                          type: string;
                          selected_start_value?: number;
                          selected_end_value?: number;
                        },
                        index: number
                      ) => {
                        // Inline data processing for each item during rendering
                        const isWon = item.won_status === 'WON';
                        const profit = isWon
                          ? Number(item.won_amount) - item.bet_amount
                          : -item.bet_amount;
                        const timestamp = new Date(item.timestamp);
                        const startPrice = parseFloat(item.start_crypto_value);
                        const endPrice = parseFloat(item.end_crypto_value);

                        return (
                          <div
                            key={index}
                            className={`p-4 rounded-lg font-[Poppins] ${
                              isWon ? 'bg-green-500/10' : 'bg-red-500/10'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <span
                                  className={
                                    isWon ? 'text-green-400' : 'text-red-400'
                                  }
                                >
                                  {item.type === 'RANGE'
                                    ? `Range: ${formatPrice(
                                        item.selected_start_value || 0
                                      )} - ${formatPrice(
                                        item.selected_end_value || 0
                                      )}`
                                    : `${item.type}`}
                                </span>
                              </div>
                              <span
                                className={`font-medium ${
                                  profit > 0 ? 'text-green-400' : 'text-red-400'
                                }`}
                              >
                                {profit > 0 ? '+' : ''}
                                {formatCoins(profit)}
                              </span>
                            </div>
                            <div className="flex items-center justify-between text-sm text-white/60">
                              <span>
                                {formatPrice(startPrice)} →{' '}
                                {formatPrice(endPrice)}
                              </span>
                              <span>{timestamp.toLocaleTimeString()}</span>
                            </div>
                          </div>
                        );
                      }
                    )}
                {hasNextPage && (
                  <PzButton
                    text="View All"
                    onClick={() => {
                      setShowTransactionModal(true);
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        {showTransactionModal && <Transactions onClose={onClose} />}
      </div>
    </>
  );
};

export default React.memo(CryptoKing);
