import React, { useState, useRef, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  style?: React.CSSProperties;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  width,
  height,
  priority = false,
  placeholder = 'empty',
  blurDataURL,
  onLoad,
  onError,
  style,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    if (priority) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px'
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority]);

  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setHasError(true);
    if (onError) {
      onError(e);
    } else {
      // Default error handling - show SVG placeholder
      const target = e.target as HTMLImageElement;
      target.src = `data:image/svg+xml;base64,${btoa(`
        <svg width="${width || 200}" height="${height || 200}" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="#374151"/>
          <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9CA3AF" font-size="14" font-family="Arial">
            Image not found
          </text>
        </svg>
      `)}`;
    }
  };

  // Generate different image formats for better optimization
  const getImageSources = (originalSrc: string) => {
    const sources = [];
    
    // AVIF format (best compression)
    if (!originalSrc.includes('.avif')) {
      sources.push({
        srcSet: originalSrc.replace(/\.(png|jpg|jpeg|webp)$/i, '.avif'),
        type: 'image/avif'
      });
    }
    
    // WebP format (good compression, wide support)
    if (!originalSrc.includes('.webp')) {
      sources.push({
        srcSet: originalSrc.replace(/\.(png|jpg|jpeg)$/i, '.webp'),
        type: 'image/webp'
      });
    }
    
    return sources;
  };

  const imageSources = getImageSources(src);

  return (
    <div 
      ref={imgRef}
      className={`relative overflow-hidden ${className}`}
      style={{ width, height, ...style }}
    >
      {/* Blur placeholder */}
      {placeholder === 'blur' && blurDataURL && !isLoaded && (
        <img
          src={blurDataURL}
          alt=""
          className="absolute inset-0 w-full h-full object-cover filter blur-sm scale-110"
          aria-hidden="true"
        />
      )}

      {/* Loading placeholder */}
      {placeholder === 'empty' && !isLoaded && !hasError && (
        <div 
          className="absolute inset-0 bg-gray-800 animate-pulse flex items-center justify-center"
          style={{ width, height }}
        >
          <div className="w-8 h-8 border-2 border-gray-600 border-t-gray-400 rounded-full animate-spin"></div>
        </div>
      )}

      {/* Main image with multiple format support */}
      {isInView && (
        <picture>
          {imageSources.map((source, index) => (
            <source key={index} srcSet={source.srcSet} type={source.type} />
          ))}
          <img
            src={src}
            alt={alt}
            loading={priority ? 'eager' : 'lazy'}
            decoding="async"
            onLoad={handleLoad}
            onError={handleError}
            className={`w-full h-full object-cover transition-opacity duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            style={{ width, height }}
            {...props}
          />
        </picture>
      )}
    </div>
  );
};

export default OptimizedImage;
