import { Eye, EyeOff, Mail, AlertCircle, CheckCircle, Info, Phone } from "lucide-react";
import React from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";

interface SignupFormProps {
  onSignup: (
    first_name: string,
    last_name: string,
    phone_number: string,
    email: string,
    password: string,
    isAdult: boolean,
    referral_code?: string
  ) => Promise<void>;
  onSwitchTab: () => void;
  isLoading: boolean;
  error: string;
  onGoogleAuth: () => void;
  referral_code?: string;
}

interface SignupFormValues {
  first_name: string;
  last_name: string;
  phone_number: string; // local Nigerian number without +234 prefix
  email: string;
  password: string;
  referral_code: string;
  isAdult: boolean;
}

const SignupSchema = Yup.object().shape({
  first_name: Yup.string().optional(),
    // .min(2, "First name must be at least 2 characters")
    // .max(50, "First name must be less than 50 characters")
    // .matches(/^[a-zA-Z\s'-]+$/, "First name can only contain letters, spaces, hyphens, and apostrophes")
    // .test('no-numbers', 'First name cannot contain numbers', value => {
    //   return !/\d/.test(value || '');
    // })
    // .required("First name is required"),
  last_name: Yup.string().optional(),
    // .min(2, "Surname must be at least 2 characters")
    // .max(50, "Surname must be less than 50 characters")
    // .matches(/^[a-zA-Z\s'-]+$/, "Surname can only contain letters, spaces, hyphens, and apostrophes")
    // .test('no-numbers', 'Surname cannot contain numbers', value => {
    //   return !/\d/.test(value || '');
    // })
    // .required("Surname is required"),
  phone_number: Yup.string()
    .matches(/^[0-9]{10}$/, "Phone number must be exactly 10 digits")
    .test('not-all-same', 'Phone number cannot be all identical digits (e.g., 1111111111)', value => {
      return !/^(\d)\1{9}$/.test(value || '');
    })
    .test('valid-start', 'Phone number must start with 7, 8, or 9', value => {
      return /^[789]/.test(value || '');
    })
    .test('not-sequential', 'Phone number cannot be sequential (e.g., ********90)', value => {
      if (!value) return true;
      const sequential = '0********9';
      const reverseSequential = '9876543210';
      return !sequential.includes(value) && !reverseSequential.includes(value);
    })
    .required("Phone number is required"),
  email: Yup.string().optional(),
    // .email("Please enter a valid email address (e.g., <EMAIL>)")
    // .max(100, "Email must be less than 100 characters")
    // .test('no-spaces', 'Email cannot contain spaces', value => {
    //   return !/\s/.test(value || '');
    // })
    // .test('valid-domain', 'Email must have a valid domain', value => {
    //   if (!value) return true;
    //   const domainRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    //   return domainRegex.test(value);
    // })
    // .required("Email is required"),
  password: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password must be less than 128 characters")
    .matches(/(?=.*[a-z])/, "Password must contain at least one lowercase letter")
    .matches(/(?=.*[A-Z])/, "Password must contain at least one uppercase letter")
    .matches(/(?=.*\d)/, "Password must contain at least one number")
    .matches(/(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/, "Password must contain at least one special character")
    .test('no-spaces', 'Password cannot contain spaces', value => {
      return !/\s/.test(value || '');
    })
    .test('not-common', 'Password is too common. Please choose a stronger password', value => {
      const commonPasswords = ['password', '********', 'qwerty123', 'password123', 'admin123'];
      return !commonPasswords.includes(value?.toLowerCase() || '');
    })
    .required("Password is required"),
  referral_code: Yup.string()
    .matches(/^[A-Za-z0-9]*$/, "Referral code can only contain letters and numbers")
    .max(20, "Referral code must be less than 20 characters")
    .optional(),
  isAdult: Yup.boolean()
    .oneOf([true], "You must confirm that you are 18 years or older to create an account")
    .required("Age confirmation is required"),
});

// Helper component for field validation feedback
const FieldValidationFeedback: React.FC<{
  error?: string;
  touched?: boolean;
  isValid?: boolean;
  helpText?: string;
}> = ({ error, touched, isValid, helpText }) => {
  if (error && touched) {
    return (
      <div className="flex items-start gap-2 mt-1 text-red-400 text-xs">
        <AlertCircle size={14} className="mt-0.5 flex-shrink-0" />
        <span>{error}</span>
      </div>
    );
  }

  if (isValid && touched) {
    return (
      <div className="flex items-center gap-2 mt-1 text-green-400 text-xs">
        <CheckCircle size={14} />
        <span>Looks good!</span>
      </div>
    );
  }

  if (helpText && !touched) {
    return (
      <div className="flex items-start gap-2 mt-1 text-white/50 text-xs">
        <Info size={14} className="mt-0.5 flex-shrink-0" />
        <span>{helpText}</span>
      </div>
    );
  }

  return null;
};

// Helper component for password strength indicator
const PasswordStrengthIndicator: React.FC<{ password: string; touched?: boolean }> = ({
  password,
  touched
}) => {
  if (!touched || !password) return null;

  const checks = [
    { test: password.length >= 8, label: "8+ chars" },
    { test: /[a-z]/.test(password), label: "lowercase" },
    { test: /[A-Z]/.test(password), label: "uppercase" },
    { test: /\d/.test(password), label: "number" },
    { test: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password), label: "special" },
  ];

  const passedChecks = checks.filter(check => check.test).length;
  const strength = passedChecks <= 2 ? 'weak' : passedChecks <= 4 ? 'medium' : 'strong';

  const strengthColors = {
    weak: 'bg-red-500',
    medium: 'bg-yellow-500',
    strong: 'bg-green-500'
  };

  const strengthTextColors = {
    weak: 'text-red-400',
    medium: 'text-yellow-400',
    strong: 'text-green-400'
  };

  return (
    <div className="mt-2">
      {/* Strength bar */}
      <div className="flex items-center gap-2 mb-2">
        <span className="text-xs text-white/60">Strength:</span>
        <div className="flex-1 bg-white/10 rounded-full h-1.5">
          <div
            className={`h-full rounded-full transition-all duration-300 ${strengthColors[strength]}`}
            style={{ width: `${(passedChecks / 5) * 100}%` }}
          />
        </div>
        <span className={`text-xs font-medium ${strengthTextColors[strength]}`}>
          {strength.charAt(0).toUpperCase() + strength.slice(1)}
        </span>
      </div>

      {/* Compact requirements */}
      <div className="flex flex-wrap gap-1">
        {checks.map((check, index) => (
          <span
            key={index}
            className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs ${check.test
                ? 'bg-green-500/20 text-green-400'
                : 'bg-white/10 text-white/50'
              } transition-colors`}
          >
            {check.test ? (
              <CheckCircle size={10} />
            ) : (
              <div className="w-2 h-2 rounded-full border border-current opacity-50" />
            )}
            {check.label}
          </span>
        ))}
      </div>
    </div>
  );
};

const SignupForm: React.FC<SignupFormProps> = (props) => {
  const { onSignup, onSwitchTab, error, isLoading, onGoogleAuth, referral_code } = props;
  const [showPassword, setShowPassword] = React.useState(false);

  const initialValues: SignupFormValues = {
    first_name: "",
    last_name: "",
    phone_number: "",
    email: "",
    password: "",
    referral_code: referral_code || "",
    isAdult: false,
  };

  const handleSubmit = async (values: SignupFormValues) => {
    const mergedPhone = `+234${values.phone_number.trim()}`;

    await onSignup(
      values.first_name,
      values.last_name,
      mergedPhone,
      values.email,
      values.password,
      values.isAdult,
      values.referral_code ? values.referral_code : undefined
    );

  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={SignupSchema}
      onSubmit={handleSubmit}
    >
      {({ errors, touched }) => (
        <Form className="space-y-6">
          {error && <p className="text-red-500 text-center">{error}</p>}

          {/* First Name */}
          {/* <div>
            <label htmlFor="first_name"   className="block text-sm font-normal font-[Poppins] mb-1">
              First Name *
            </label>
            <div className="relative">
              <Field
                id="first_name"
                name="first_name"
                type="text"
                placeholder="e.g., John"
                className={`w-full bg-white/10 border ${errors.first_name && touched.first_name
                    ? "border-red-500"
                    : touched.first_name && !errors.first_name
                      ? "border-green-500"
                      : "border-white/20"
                  } rounded-lg pl-4 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF] transition-colors`}
              />
            </div>
            <FieldValidationFeedback
              error={errors.first_name}
              touched={touched.first_name}
              isValid={!errors.first_name && touched.first_name}
              helpText="Enter your legal first name (2-50 characters, letters only)"
            />
          </div> */}

          {/* Surname */}
          {/* <div>
            <label htmlFor="last_name"   className="block text-sm font-normal font-[Poppins] mb-1">
              Surname *
            </label>
            <div className="relative">
              <Field
                id="last_name"
                name="last_name"
                type="text"
                placeholder="e.g., Smith"
                className={`w-full bg-white/10 border ${errors.last_name && touched.last_name
                    ? "border-red-500"
                    : touched.last_name && !errors.last_name
                      ? "border-green-500"
                      : "border-white/20"
                  } rounded-lg pl-4 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF] transition-colors`}
              />
            </div>
            <FieldValidationFeedback
              error={errors.last_name}
              touched={touched.last_name}
              isValid={!errors.last_name && touched.last_name}
              helpText="Enter your legal surname (2-50 characters, letters only)"
            />
          </div> */}

          {/* Phone */}
          <div>
            <label htmlFor="phone_number"   className="block text-sm font-normal font-[Poppins] mb-1">
              Phone Number *
            </label>
            <div className="flex space-x-2">
              <div className="flex items-center px-3 rounded-l-lg border border-white/20 bg-white/10 text-white/80">
                <Phone size={16} className="mr-1" />
                <span>+234</span>
              </div>
              <Field
                id="phone_number"
                name="phone_number"
                type="tel"
                placeholder="8031234567"
                maxLength={10}
                className={`w-full bg-white/10 border ${errors.phone_number && touched.phone_number
                    ? "border-red-500"
                    : touched.phone_number && !errors.phone_number
                      ? "border-green-500"
                      : "border-white/20"
                  } rounded-r-lg py-2 px-3 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF] transition-colors`}
                onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, '').slice(0, 10);
                }}
              />
            </div>
            <FieldValidationFeedback
              error={errors.phone_number}
              touched={touched.phone_number}
              isValid={!errors.phone_number && touched.phone_number}
              helpText="Enter your Nigerian phone number (10 digits, starting with 7, 8, or 9)"
            />
          </div>

          {/* Email */}
          {/* <div>
            <label htmlFor="email"   className="block text-sm font-normal font-[Poppins] mb-1">
              Email Address *
            </label>
            <div className="relative">
              <Field
                id="email"
                name="email"
                type="email"
                placeholder="e.g., <EMAIL>"
                className={`w-full bg-white/10 border ${errors.email && touched.email
                    ? "border-red-500"
                    : touched.email && !errors.email
                      ? "border-green-500"
                      : "border-white/20"
                  } rounded-lg pl-10 pr-4 py-2 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF] transition-colors`}
              />
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-white/40" size={18} />
            </div>
            <FieldValidationFeedback
              error={errors.email}
              touched={touched.email}
              isValid={!errors.email && touched.email}
              helpText="Enter a valid email address (we'll send you important updates)"
            />
          </div> */}

          {/* Password */}
          <div>
            <label htmlFor="password"   className="block text-sm font-normal font-[Poppins] mb-1">
              Password *
            </label>
            <div className="relative">
              <Field name="password">
                {({ field, form }: any) => (
                  <>
                    <input
                      {...field}
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="e.g., Abcd@123"
                      className={`w-full bg-white/10 border ${errors.password && touched.password
                          ? "border-red-500"
                          : touched.password && !errors.password
                            ? "border-green-500"
                            : "border-white/20"
                        } rounded-lg pl-4 pr-10 py-2 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF] transition-colors`}
                      style={{
                        WebkitAppearance: 'none',
                        MozAppearance: 'none',
                        appearance: 'none',
                        WebkitTextSecurity: showPassword ? 'none' : 'disc',
                        fontFamily: 'inherit'
                      }}
                      autoComplete="new-password"
                      data-lpignore="true"
                      data-form-type="password"
                    />

                    {/* <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-white/40 hover:text-white/60 transition-colors"
                      aria-label={showPassword ? "Hide password" : "Show password"}
                    >
                      {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                    </button> */}
                    {/* Show error message or password strength indicator */}
                    {errors.password && touched.password ? (
                      <FieldValidationFeedback
                        error={errors.password}
                        touched={touched.password}
                        isValid={false}
                      />
                    ) : touched.password ? (
                      <PasswordStrengthIndicator
                        password={field.value}
                        touched={touched.password}
                      />
                    ) : (
                      <FieldValidationFeedback
                        helpText="Password must be at least 8 characters long and include uppercase, lowercase, a number, and a special character."
                      />
                    )}
                  </>
                )}
              </Field>
            </div>
          </div>

          {/* Referral Code */}
          <div>
            <label htmlFor="referral_code"   className="block text-sm font-normal font-[Poppins] mb-1">
              Referral Code (Optional)
            </label>
            <div className="relative">
              <Field
                id="referral_code"
                name="referral_code"
                type="text"
                placeholder="e.g., FRIEND123"
                className={`w-full bg-white/10 border ${errors.referral_code && touched.referral_code
                    ? "border-red-500"
                    : touched.referral_code && !errors.referral_code
                      ? "border-green-500"
                      : "border-white/20"
                  } rounded-lg py-2 px-4 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF] transition-colors`}
              />
            </div>
            <FieldValidationFeedback
              error={errors.referral_code}
              touched={touched.referral_code}
              isValid={!errors.referral_code && touched.referral_code}
              helpText="Enter a referral code if you have one (letters and numbers only)"
            />
          </div>

          {/* Over 18 Checkbox */}
          <div>
            <div className="flex items-center">
              <Field
                id="isAdult"
                name="isAdult"
                type="checkbox"
                className={`h-4 w-4 text-[#ED0CFF] bg-white/10 border ${errors.isAdult && touched.isAdult ? "border-red-500" : "border-white/20"
                  } rounded focus:ring-[#ED0CFF]`} />
              <label htmlFor="isAdult" className="ml-2 text-sm text-white/80">
                I am 18 years or older *
              </label>
            </div>
            <p className="text-xs text-white/60 mt-1">
              By signing up, you confirm that you’re over 18, accept our Terms & Conditions, and acknowledge our Responsible Gambling Policy.
            </p>
            <FieldValidationFeedback
              error={errors.isAdult}
              touched={touched.isAdult}
              isValid={!errors.isAdult && touched.isAdult}
              helpText="You must be 18 or older to create an account"
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full py-2 rounded-lg font-normal uppercase font-[Anton] tracking-wider text-white flex justify-center items-center ${isLoading ? "bg-[#ED0CFF] cursor-not-allowed" : "bg-[#ED0CFF]"
              }`}
          >
            {isLoading ? (
              <svg
                className="animate-spin h-5 w-5"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                />
              </svg>
            ) : (
              "SIGN UP"
            )}
          </button>

          {/* Switch to Login */}
          <div className="text-center text-sm text-white/60">
            Do you have an account?{' '}
            <button
              type="button"
              className="text-white/90 hover:text-white"
              onClick={onSwitchTab}
            >
              Sign in
            </button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default SignupForm;
